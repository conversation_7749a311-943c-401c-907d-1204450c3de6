import { createDoc, readDoc, readCollection, updateDocFields, deleteDocByPath } from "./firestoreService";
import { Timestamp } from "firebase/firestore";

export interface LearningConcept {
  title: string;
  description: string;
  prerequisites: string[];
  focusArea: string;
}

export interface SavedLearningPlan {
  id?: string;
  userId: string;
  certificateId: string;
  certificateName: string;
  keyword: string;
  concepts: LearningConcept[];
  intensity: "detailed" | "general" | "simple";
  generatedAt: string;
  savedAt?: Timestamp | Date;
  lastModified?: Timestamp | Date;
  isCompleted?: boolean;
  progress?: {
    completedConcepts: number;
    totalConcepts: number;
    lastAccessedAt?: Timestamp | Date;
  };
  metadata?: {
    version: number;
    tags?: string[];
    notes?: string;
  };
}

export interface SaveLearningPlanInput {
  userId: string;
  certificateId: string;
  certificateName: string;
  keyword: string;
  concepts: LearningConcept[];
  intensity: "detailed" | "general" | "simple";
  generatedAt: string;
  tags?: string[];
  notes?: string;
}

export interface UpdateLearningPlanInput {
  keyword?: string;
  concepts?: LearningConcept[];
  intensity?: "detailed" | "general" | "simple";
  tags?: string[];
  notes?: string;
  isCompleted?: boolean;
  progress?: {
    completedConcepts: number;
    totalConcepts: number;
  };
}

/**
 * Save a new learning plan for a user
 */
export async function saveLearningPlan(input: SaveLearningPlanInput): Promise<string> {
  const planId = `${input.certificateId}_${input.keyword}_${Date.now()}`.replace(/[^a-zA-Z0-9_]/g, '_');
  const path = `users/${input.userId}/learningPlans/${planId}`;
  
  const planData: Omit<SavedLearningPlan, 'id'> = {
    userId: input.userId,
    certificateId: input.certificateId,
    certificateName: input.certificateName,
    keyword: input.keyword,
    concepts: input.concepts,
    intensity: input.intensity,
    generatedAt: input.generatedAt,
    isCompleted: false,
    progress: {
      completedConcepts: 0,
      totalConcepts: input.concepts.length,
    },
    metadata: {
      version: 1,
      tags: input.tags || [],
      notes: input.notes || "",
    },
  };

  await createDoc(path, planData);
  return planId;
}

/**
 * Get a specific learning plan by ID
 */
export async function getLearningPlan(userId: string, planId: string): Promise<SavedLearningPlan | null> {
  const path = `users/${userId}/learningPlans/${planId}`;
  const plan = await readDoc<SavedLearningPlan>(path);
  return plan ? { ...plan, id: planId } : null;
}

/**
 * Get all learning plans for a user
 */
export async function getUserLearningPlans(userId: string): Promise<SavedLearningPlan[]> {
  const path = `users/${userId}/learningPlans`;
  const plans = await readCollection<SavedLearningPlan>(path);
  return plans.map(plan => ({ ...plan, id: plan.id }));
}

/**
 * Get learning plans for a specific certificate
 */
export async function getCertificateLearningPlans(userId: string, certificateId: string): Promise<SavedLearningPlan[]> {
  const allPlans = await getUserLearningPlans(userId);
  return allPlans.filter(plan => plan.certificateId === certificateId);
}

/**
 * Update an existing learning plan
 */
export async function updateLearningPlan(
  userId: string, 
  planId: string, 
  updates: UpdateLearningPlanInput
): Promise<void> {
  const path = `users/${userId}/learningPlans/${planId}`;
  
  const updateData: Partial<SavedLearningPlan> = {
    ...updates,
    metadata: updates.tags || updates.notes ? {
      version: 1, // Will be incremented in a real implementation
      tags: updates.tags,
      notes: updates.notes,
    } : undefined,
  };

  await updateDocFields(path, updateData);
}

/**
 * Delete a learning plan
 */
export async function deleteLearningPlan(userId: string, planId: string): Promise<void> {
  const path = `users/${userId}/learningPlans/${planId}`;
  await deleteDocByPath(path);
}

/**
 * Update learning progress
 */
export async function updateLearningProgress(
  userId: string, 
  planId: string, 
  completedConcepts: number
): Promise<void> {
  const path = `users/${userId}/learningPlans/${planId}`;
  const plan = await getLearningPlan(userId, planId);
  
  if (!plan) {
    throw new Error("Learning plan not found");
  }

  const isCompleted = completedConcepts >= plan.concepts.length;
  
  await updateDocFields(path, {
    progress: {
      completedConcepts,
      totalConcepts: plan.concepts.length,
      lastAccessedAt: new Date(),
    },
    isCompleted,
  });
}

/**
 * Check if a learning plan already exists for a keyword and certificate
 */
export async function findExistingPlan(
  userId: string, 
  certificateId: string, 
  keyword: string
): Promise<SavedLearningPlan | null> {
  const plans = await getCertificateLearningPlans(userId, certificateId);
  return plans.find(plan => 
    plan.keyword.toLowerCase().trim() === keyword.toLowerCase().trim()
  ) || null;
}
