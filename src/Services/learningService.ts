export interface ConceptExplanation {
  title: string;
  introduction: string;
  keyPoints: string[];
  coreDefinition: string;
  realWorldContext: string;
  nextSteps: string[];
}

export interface PracticalScenario {
  title: string;
  context: string;
  challenge: string;
  steps: {
    step: number;
    description: string;
    hint?: string;
  }[];
  expectedOutcome: string;
  keyLearnings: string[];
}

export interface InteractiveElement {
  type: "question" | "task" | "reflection";
  content: string;
  expectedResponse?: string;
}

export interface LearningSession {
  conceptName: string;
  explanation: ConceptExplanation;
  practicalScenarioPrompt: string;
  scenario?: PracticalScenario;
  interactiveElements?: InteractiveElement[];
  startTime: number;
  currentPhase: "explanation" | "scenario" | "completed";
}

export class LearningService {
  private static instance: LearningService;
  private sessions: Map<string, LearningSession> = new Map();

  static getInstance(): LearningService {
    if (!LearningService.instance) {
      LearningService.instance = new LearningService();
    }
    return LearningService.instance;
  }

  async generateConceptExplanation(
    conceptName: string,
    intensity: "simple" | "general" | "detailed" = "general"
  ): Promise<{ explanation: ConceptExplanation; practicalScenarioPrompt: string }> {
    try {
      const response = await fetch("/api/ai/learning/concept", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          conceptName,
          intensity,
        }),
      });

      if (!response.ok) {
        throw new Error(`Failed to generate concept explanation: ${response.statusText}`);
      }

      const data = await response.json();
      
      if (!data.success) {
        throw new Error(data.error || "Failed to generate concept explanation");
      }

      return data.data;
    } catch (error) {
      console.error("Error generating concept explanation:", error);
      throw error;
    }
  }

  async generatePracticalScenario(
    conceptName: string,
    scenarioPrompt: string,
    difficulty: "beginner" | "intermediate" | "advanced" = "intermediate"
  ): Promise<{ scenario: PracticalScenario; interactiveElements: InteractiveElement[] }> {
    try {
      const response = await fetch("/api/ai/learning/scenario", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          conceptName,
          scenarioPrompt,
          difficulty,
        }),
      });

      if (!response.ok) {
        throw new Error(`Failed to generate practical scenario: ${response.statusText}`);
      }

      const data = await response.json();
      
      if (!data.success) {
        throw new Error(data.error || "Failed to generate practical scenario");
      }

      return data.data;
    } catch (error) {
      console.error("Error generating practical scenario:", error);
      throw error;
    }
  }

  createLearningSession(conceptName: string): string {
    const sessionId = `${conceptName}-${Date.now()}`;
    const session: LearningSession = {
      conceptName,
      explanation: {
        title: "",
        introduction: "",
        keyPoints: [],
        coreDefinition: "",
        realWorldContext: "",
        nextSteps: [],
      },
      practicalScenarioPrompt: "",
      startTime: Date.now(),
      currentPhase: "explanation",
    };

    this.sessions.set(sessionId, session);
    return sessionId;
  }

  getLearningSession(sessionId: string): LearningSession | null {
    return this.sessions.get(sessionId) || null;
  }

  updateLearningSession(sessionId: string, updates: Partial<LearningSession>): void {
    const session = this.sessions.get(sessionId);
    if (session) {
      this.sessions.set(sessionId, { ...session, ...updates });
    }
  }

  deleteLearningSession(sessionId: string): void {
    this.sessions.delete(sessionId);
  }

  getSessionProgress(sessionId: string): {
    phase: string;
    timeSpent: number;
    completionPercentage: number;
  } {
    const session = this.sessions.get(sessionId);
    if (!session) {
      return { phase: "unknown", timeSpent: 0, completionPercentage: 0 };
    }

    const timeSpent = Math.round((Date.now() - session.startTime) / 1000);
    
    let completionPercentage = 0;
    switch (session.currentPhase) {
      case "explanation":
        completionPercentage = 25;
        break;
      case "scenario":
        completionPercentage = 75;
        break;
      case "completed":
        completionPercentage = 100;
        break;
    }

    return {
      phase: session.currentPhase,
      timeSpent,
      completionPercentage,
    };
  }
}
