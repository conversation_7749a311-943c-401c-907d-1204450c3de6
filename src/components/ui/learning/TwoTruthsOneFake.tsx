"use client";

import { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Check, X, RotateCcw, HelpCircle } from "lucide-react";

export interface TwoTruthsOneFakeData {
  id: string;
  question: string;
  statements: [string, string, string];
  fakeIndex: 0 | 1 | 2;
  explanations: [string, string, string];
  category?: string;
  difficulty?: "easy" | "medium" | "hard";
  tags?: string[];
}

interface TwoTruthsOneFakeProps {
  data: TwoTruthsOneFakeData;
  className?: string;
  onAnswer?: (isCorrect: boolean, selectedIndex: number) => void;
}

const difficultyColors = {
  easy: "var(--bright-green)",
  medium: "var(--lime-green)", 
  hard: "var(--emerald-deep)"
};

export default function TwoTruthsOneFake({ 
  data, 
  className = "", 
  onAnswer
}: TwoTruthsOneFakeProps) {
  const [selectedIndex, setSelectedIndex] = useState<number | null>(null);
  const [showExplanation, setShowExplanation] = useState(false);

  const handleAnswer = (index: number) => {
    setSelectedIndex(index);
    setShowExplanation(true);
    const isCorrect = index === data.fakeIndex;
    onAnswer?.(isCorrect, index);
  };

  const handleReset = () => {
    setSelectedIndex(null);
    setShowExplanation(false);
  };

  const isCorrect = selectedIndex === data.fakeIndex;

  return (
    <div className={`w-96 h-96 ${className}`}>
      <div className="relative w-full h-full">
        <div
          className="w-full h-full rounded-2xl shadow-lg border p-6 flex flex-col"
          style={{
            backgroundColor: "var(--white)",
            borderColor: "var(--color-border)",
            boxShadow: "0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)"
          }}
        >
          {/* Header */}
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-2">
              <div
                className="p-2 rounded-lg"
                style={{ backgroundColor: "var(--lime-green)", color: "var(--white)" }}
              >
                <HelpCircle className="w-4 h-4" />
              </div>
              {data.category && (
                <span
                  className="text-xs font-medium px-2 py-1 rounded-full"
                  style={{
                    backgroundColor: "var(--color-muted)",
                    color: "var(--charcoal)"
                  }}
                >
                  {data.category}
                </span>
              )}
            </div>
            {data.difficulty && (
              <div
                className="w-3 h-3 rounded-full"
                style={{ backgroundColor: difficultyColors[data.difficulty] }}
                title={`Difficulty: ${data.difficulty}`}
              />
            )}
          </div>

          {/* Question */}
          <div className="mb-4">
            <p
              className="text-sm font-semibold text-center"
              style={{ color: "var(--charcoal)" }}
            >
              {data.question}
            </p>
          </div>

          {/* Statements or Results */}
          <div className="flex-1">
            <AnimatePresence mode="wait">
              {!showExplanation ? (
                <motion.div
                  key="statements"
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -10 }}
                  className="space-y-2"
                >
                  {data.statements.map((statement, index) => (
                    <button
                      key={`statement-${index}`}
                      onClick={() => handleAnswer(index)}
                      className="w-full p-3 text-left rounded-lg text-xs leading-relaxed transition-all duration-200 hover:scale-[1.01] hover:shadow-sm"
                      style={{
                        backgroundColor: "var(--color-muted)",
                        color: "var(--charcoal)",
                        border: "1px solid var(--color-border)"
                      }}
                    >
                      <span className="font-medium">{String.fromCharCode(65 + index)}.</span> {statement}
                    </button>
                  ))}
                </motion.div>
              ) : (
                <motion.div
                  key="results"
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -10 }}
                  className="space-y-3"
                >
                  {/* Result Indicator */}
                  <div
                    className="flex items-center justify-center gap-2 py-2 rounded-lg"
                    style={{
                      backgroundColor: isCorrect ? "var(--bright-green)" : "var(--grey)",
                      color: "var(--white)"
                    }}
                  >
                    {isCorrect ? (
                      <>
                        <Check className="w-4 h-4" />
                        <span className="text-sm font-semibold">Correct! You found the fake.</span>
                      </>
                    ) : (
                      <>
                        <X className="w-4 h-4" />
                        <span className="text-sm font-semibold">Incorrect. That was true!</span>
                      </>
                    )}
                  </div>

                  {/* Explanations */}
                  <div className="space-y-2 max-h-32 overflow-y-auto">
                    {data.statements.map((statement, index) => (
                      <div
                        key={`explanation-${index}`}
                        className={`p-2 rounded-lg text-xs ${
                          index === data.fakeIndex ? 'ring-2' : ''
                        }`}
                        style={{
                          backgroundColor: index === data.fakeIndex 
                            ? "var(--grey)15" 
                            : "var(--bright-green)15",
                          color: "var(--charcoal)",
                          ringColor: index === data.fakeIndex ? "var(--grey)" : "transparent"
                        }}
                      >
                        <div className="flex items-start gap-2">
                          <span className="font-medium flex-shrink-0">
                            {String.fromCharCode(65 + index)}.
                          </span>
                          <div>
                            <div className="font-medium mb-1">
                              {index === data.fakeIndex ? (
                                <span className="text-red-600">FAKE</span>
                              ) : (
                                <span className="text-green-600">TRUE</span>
                              )}
                            </div>
                            <div>{data.explanations[index]}</div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>

                  {/* Reset Button */}
                  <button
                    onClick={handleReset}
                    className="w-full flex items-center justify-center gap-2 py-2 rounded-lg text-xs font-medium transition-colors duration-200"
                    style={{
                      backgroundColor: "var(--emerald)",
                      color: "var(--white)"
                    }}
                  >
                    <RotateCcw className="w-3 h-3" />
                    Try Again
                  </button>
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        </div>
      </div>
    </div>
  );
}

// Example data for preview
export const exampleTwoTruthsOneFakeData: TwoTruthsOneFakeData[] = [
  {
    id: "example-ttof-1",
    question: "Which statement about web browsers is FALSE?",
    statements: [
      "Chrome was first released by Google in 2008",
      "Firefox is developed by the Mozilla Foundation", 
      "Safari was originally created by Microsoft"
    ],
    fakeIndex: 2,
    explanations: [
      "True - Google Chrome was indeed first released in September 2008.",
      "True - Firefox is developed and maintained by the Mozilla Foundation.",
      "False - Safari was created by Apple, not Microsoft. Microsoft's browser is Edge (formerly Internet Explorer)."
    ],
    category: "Web Technology",
    difficulty: "medium",
    tags: ["Browsers", "History"]
  }
];
