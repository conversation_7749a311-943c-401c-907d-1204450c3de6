"use client";

import { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { MessageCircle, User, Bot, ArrowRight, Lightbulb, RotateCcw, CheckCircle, List, Table, Target, BarChart3, GitBranch, Zap } from "lucide-react";

export interface MentorMeData {
  id: string;
  topic: string;
  conversation: {
    id: string;
    type: "mentor" | "user";
    message: string;
    content?: {
      type: "text" | "table" | "list" | "comparison" | "steps" | "highlight" | "quiz" | "progress" | "diagram" | "tips";
      data: any;
    };
    timestamp?: string;
  }[];
  category?: string;
  difficulty?: "easy" | "medium" | "hard";
  tags?: string[];
}

interface MentorMeProps {
  data: MentorMeData;
  className?: string;
  onInteraction?: (message: string) => void;
}

const difficultyColors = {
  easy: "var(--bright-green)",
  medium: "var(--lime-green)",
  hard: "var(--emerald-deep)"
};

// Rich Content Renderer Component
const RichContentRenderer = ({ content, message }: { content?: any; message: string }) => {
  if (!content) {
    return <p className="text-sm leading-relaxed">{message}</p>;
  }

  switch (content.type) {
    case "table":
      return (
        <div className="space-y-2">
          <p className="text-sm leading-relaxed">{message}</p>
          <div className="overflow-x-auto">
            <table className="w-full text-xs border-collapse">
              <thead>
                <tr style={{ backgroundColor: "var(--emerald)15" }}>
                  {content.data.headers.map((header: string, index: number) => (
                    <th
                      key={`header-${index}`}
                      className="border px-2 py-1 text-left font-semibold"
                      style={{
                        borderColor: "var(--color-border)",
                        color: "var(--charcoal)"
                      }}
                    >
                      {header}
                    </th>
                  ))}
                </tr>
              </thead>
              <tbody>
                {content.data.rows.map((row: string[], rowIndex: number) => (
                  <tr key={`row-${rowIndex}`}>
                    {row.map((cell: string, cellIndex: number) => (
                      <td
                        key={`cell-${rowIndex}-${cellIndex}`}
                        className="border px-2 py-1"
                        style={{
                          borderColor: "var(--color-border)",
                          color: "var(--charcoal)"
                        }}
                      >
                        {cell}
                      </td>
                    ))}
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      );

    case "list":
      return (
        <div className="space-y-2">
          <p className="text-sm leading-relaxed">{message}</p>
          <ul className="space-y-1">
            {content.data.items.map((item: string, index: number) => (
              <li key={`list-item-${index}`} className="flex items-start gap-2 text-xs">
                <CheckCircle className="w-3 h-3 mt-0.5 flex-shrink-0" style={{ color: "var(--bright-green)" }} />
                <span style={{ color: "var(--charcoal)" }}>{item}</span>
              </li>
            ))}
          </ul>
        </div>
      );

    case "quiz":
      return (
        <div className="space-y-2">
          <p className="text-sm leading-relaxed">{message}</p>
          <div
            className="p-3 rounded-lg border-l-4"
            style={{
              backgroundColor: "var(--bright-green)10",
              borderLeftColor: "var(--bright-green)"
            }}
          >
            <div className="flex items-start gap-2">
              <Target className="w-4 h-4 mt-0.5" style={{ color: "var(--bright-green)" }} />
              <div className="flex-1">
                <h4 className="text-xs font-semibold mb-2" style={{ color: "var(--bright-green)" }}>
                  Quick Check: {content.data.question}
                </h4>
                <div className="space-y-1">
                  {content.data.options.map((option: string, index: number) => (
                    <div key={`quiz-option-${index}`} className="flex items-center gap-2">
                      <div
                        className="w-4 h-4 rounded-full border-2 flex items-center justify-center text-xs"
                        style={{ borderColor: "var(--bright-green)" }}
                      >
                        {String.fromCharCode(65 + index)}
                      </div>
                      <span className="text-xs" style={{ color: "var(--charcoal)" }}>
                        {option}
                      </span>
                    </div>
                  ))}
                </div>
                <p className="text-xs mt-2 font-medium" style={{ color: "var(--bright-green)" }}>
                  Answer: {content.data.answer}
                </p>
              </div>
            </div>
          </div>
        </div>
      );

    case "comparison":
      return (
        <div className="space-y-2">
          <p className="text-sm leading-relaxed">{message}</p>
          <div className="grid grid-cols-2 gap-2">
            <div
              className="p-2 rounded-lg"
              style={{ backgroundColor: "var(--bright-green)15" }}
            >
              <h4 className="text-xs font-semibold mb-1" style={{ color: "var(--bright-green)" }}>
                ✓ {content.data.positive.title}
              </h4>
              <p className="text-xs" style={{ color: "var(--charcoal)" }}>
                {content.data.positive.description}
              </p>
            </div>
            <div
              className="p-2 rounded-lg"
              style={{ backgroundColor: "var(--grey)15" }}
            >
              <h4 className="text-xs font-semibold mb-1" style={{ color: "var(--grey)" }}>
                ✗ {content.data.negative.title}
              </h4>
              <p className="text-xs" style={{ color: "var(--charcoal)" }}>
                {content.data.negative.description}
              </p>
            </div>
          </div>
        </div>
      );

    case "steps":
      return (
        <div className="space-y-2">
          <p className="text-sm leading-relaxed">{message}</p>
          <div className="space-y-2">
            {content.data.steps.map((step: { title: string; description: string }, index: number) => (
              <div key={`step-${index}`} className="flex gap-2">
                <div
                  className="w-5 h-5 rounded-full flex items-center justify-center text-xs font-bold flex-shrink-0"
                  style={{
                    backgroundColor: "var(--emerald)",
                    color: "var(--white)"
                  }}
                >
                  {index + 1}
                </div>
                <div>
                  <h4 className="text-xs font-semibold" style={{ color: "var(--charcoal)" }}>
                    {step.title}
                  </h4>
                  <p className="text-xs" style={{ color: "var(--grey)" }}>
                    {step.description}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>
      );

    case "highlight":
      return (
        <div className="space-y-2">
          <p className="text-sm leading-relaxed">{message}</p>
          <div
            className="p-3 rounded-lg border-l-4"
            style={{
              backgroundColor: "var(--emerald)10",
              borderLeftColor: "var(--emerald)"
            }}
          >
            <div className="flex items-start gap-2">
              <Lightbulb className="w-4 h-4 mt-0.5" style={{ color: "var(--emerald)" }} />
              <div>
                <h4 className="text-xs font-semibold mb-1" style={{ color: "var(--emerald)" }}>
                  {content.data.title}
                </h4>
                <p className="text-xs" style={{ color: "var(--charcoal)" }}>
                  {content.data.description}
                </p>
              </div>
            </div>
          </div>
        </div>
      );

    case "progress":
      return (
        <div className="space-y-2">
          <p className="text-sm leading-relaxed">{message}</p>
          <div
            className="p-3 rounded-lg"
            style={{ backgroundColor: "var(--color-muted)" }}
          >
            <div className="flex items-center gap-2 mb-2">
              <BarChart3 className="w-4 h-4" style={{ color: "var(--emerald)" }} />
              <span className="text-xs font-semibold" style={{ color: "var(--charcoal)" }}>
                Learning Progress
              </span>
            </div>
            <div className="space-y-2">
              {content.data.skills.map((skill: { name: string; level: number }, index: number) => (
                <div key={`skill-${index}`} className="space-y-1">
                  <div className="flex justify-between items-center">
                    <span className="text-xs" style={{ color: "var(--charcoal)" }}>
                      {skill.name}
                    </span>
                    <span className="text-xs font-medium" style={{ color: "var(--emerald)" }}>
                      {skill.level}%
                    </span>
                  </div>
                  <div
                    className="w-full h-2 rounded-full"
                    style={{ backgroundColor: "var(--color-border)" }}
                  >
                    <div
                      className="h-2 rounded-full transition-all duration-500"
                      style={{
                        backgroundColor: "var(--emerald)",
                        width: `${skill.level}%`
                      }}
                    />
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      );

    case "diagram":
      return (
        <div className="space-y-2">
          <p className="text-sm leading-relaxed">{message}</p>
          <div
            className="p-3 rounded-lg"
            style={{ backgroundColor: "var(--color-muted)" }}
          >
            <div className="flex items-center gap-2 mb-3">
              <GitBranch className="w-4 h-4" style={{ color: "var(--lime-green)" }} />
              <span className="text-xs font-semibold" style={{ color: "var(--charcoal)" }}>
                {content.data.title}
              </span>
            </div>
            <div className="space-y-2">
              {content.data.nodes.map((node: { label: string; description: string; connections?: string[] }, index: number) => (
                <div key={`node-${index}`} className="relative">
                  <div
                    className="p-2 rounded-lg border-2"
                    style={{
                      backgroundColor: "var(--white)",
                      borderColor: "var(--lime-green)"
                    }}
                  >
                    <h5 className="text-xs font-semibold" style={{ color: "var(--charcoal)" }}>
                      {node.label}
                    </h5>
                    <p className="text-xs" style={{ color: "var(--grey)" }}>
                      {node.description}
                    </p>
                  </div>
                  {index < content.data.nodes.length - 1 && (
                    <div className="flex justify-center my-1">
                      <ArrowRight className="w-3 h-3" style={{ color: "var(--lime-green)" }} />
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        </div>
      );

    case "tips":
      return (
        <div className="space-y-2">
          <p className="text-sm leading-relaxed">{message}</p>
          <div
            className="p-3 rounded-lg"
            style={{ backgroundColor: "var(--bright-green)10" }}
          >
            <div className="flex items-center gap-2 mb-2">
              <Zap className="w-4 h-4" style={{ color: "var(--bright-green)" }} />
              <span className="text-xs font-semibold" style={{ color: "var(--bright-green)" }}>
                Pro Tips
              </span>
            </div>
            <div className="grid grid-cols-1 gap-2">
              {content.data.tips.map((tip: { icon: string; title: string; description: string }, index: number) => (
                <div key={`tip-${index}`} className="flex items-start gap-2">
                  <span className="text-sm">{tip.icon}</span>
                  <div>
                    <h5 className="text-xs font-semibold" style={{ color: "var(--charcoal)" }}>
                      {tip.title}
                    </h5>
                    <p className="text-xs" style={{ color: "var(--grey)" }}>
                      {tip.description}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      );

    default:
      return <p className="text-sm leading-relaxed">{message}</p>;
  }
};

export default function MentorMe({ 
  data, 
  className = "", 
  onInteraction
}: MentorMeProps) {
  const [currentStep, setCurrentStep] = useState(0);
  const [userInput, setUserInput] = useState("");
  const [isCompleted, setIsCompleted] = useState(false);

  const handleNext = () => {
    if (currentStep < data.conversation.length - 1) {
      setCurrentStep(currentStep + 1);
    } else {
      setIsCompleted(true);
    }
  };

  const handleUserResponse = () => {
    if (userInput.trim()) {
      onInteraction?.(userInput);
      setUserInput("");
      handleNext();
    }
  };

  const handleReset = () => {
    setCurrentStep(0);
    setUserInput("");
    setIsCompleted(false);
  };

  const currentMessage = data.conversation[currentStep];
  const isUserTurn = currentMessage?.type === "user";

  return (
    <div className={`w-96 h-96 ${className}`}>
      <div className="relative w-full h-full">
        <div
          className="w-full h-full rounded-2xl shadow-lg border flex flex-col"
          style={{
            backgroundColor: "var(--white)",
            borderColor: "var(--color-border)",
            boxShadow: "0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)"
          }}
        >
          {/* Header */}
          <div
            className="flex items-center justify-between p-4 border-b"
            style={{ borderColor: "var(--color-border)" }}
          >
            <div className="flex items-center gap-2">
              <div
                className="p-2 rounded-lg"
                style={{ backgroundColor: "var(--emerald-deep)", color: "var(--white)" }}
              >
                <MessageCircle className="w-4 h-4" />
              </div>
              <div>
                <h3
                  className="text-sm font-semibold"
                  style={{ color: "var(--charcoal)" }}
                >
                  AI Mentor
                </h3>
                {data.category && (
                  <p
                    className="text-xs"
                    style={{ color: "var(--grey)" }}
                  >
                    {data.category}
                  </p>
                )}
              </div>
            </div>
            {data.difficulty && (
              <div
                className="w-3 h-3 rounded-full"
                style={{ backgroundColor: difficultyColors[data.difficulty] }}
                title={`Difficulty: ${data.difficulty}`}
              />
            )}
          </div>

          {/* Chat Area */}
          <div className="flex-1 p-4 overflow-y-auto">
            <AnimatePresence mode="wait">
              {!isCompleted ? (
                <motion.div
                  key={`step-${currentStep}`}
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -10 }}
                  className="space-y-3"
                >
                  {/* Topic Introduction */}
                  {currentStep === 0 && (
                    <div
                      className="p-3 rounded-lg"
                      style={{
                        backgroundColor: "var(--emerald)15",
                        border: "1px solid var(--emerald)"
                      }}
                    >
                      <div className="flex items-center gap-2 mb-2">
                        <Lightbulb className="w-4 h-4" style={{ color: "var(--emerald)" }} />
                        <span
                          className="text-sm font-semibold"
                          style={{ color: "var(--emerald)" }}
                        >
                          Learning Topic
                        </span>
                      </div>
                      <p
                        className="text-sm"
                        style={{ color: "var(--charcoal)" }}
                      >
                        {data.topic}
                      </p>
                    </div>
                  )}

                  {/* Current Message */}
                  {currentMessage && (
                    <div className={`flex ${currentMessage.type === "user" ? "justify-end" : "justify-start"}`}>
                      <div
                        className={`max-w-[80%] p-3 rounded-lg ${
                          currentMessage.type === "user" ? "rounded-br-sm" : "rounded-bl-sm"
                        }`}
                        style={{
                          backgroundColor: currentMessage.type === "user" 
                            ? "var(--emerald)" 
                            : "var(--color-muted)",
                          color: currentMessage.type === "user" 
                            ? "var(--white)" 
                            : "var(--charcoal)"
                        }}
                      >
                        <div className="flex items-center gap-2 mb-1">
                          {currentMessage.type === "user" ? (
                            <User className="w-3 h-3" />
                          ) : (
                            <Bot className="w-3 h-3" />
                          )}
                          <span className="text-xs font-medium">
                            {currentMessage.type === "user" ? "You" : "AI Mentor"}
                          </span>
                          {currentMessage.content && currentMessage.type === "mentor" && (
                            <div className="flex items-center gap-1 ml-auto">
                              {currentMessage.content.type === "table" && <Table className="w-3 h-3 opacity-60" />}
                              {currentMessage.content.type === "list" && <List className="w-3 h-3 opacity-60" />}
                              {currentMessage.content.type === "quiz" && <Target className="w-3 h-3 opacity-60" />}
                              {currentMessage.content.type === "steps" && <CheckCircle className="w-3 h-3 opacity-60" />}
                              {currentMessage.content.type === "highlight" && <Lightbulb className="w-3 h-3 opacity-60" />}
                              {currentMessage.content.type === "progress" && <BarChart3 className="w-3 h-3 opacity-60" />}
                              {currentMessage.content.type === "diagram" && <GitBranch className="w-3 h-3 opacity-60" />}
                              {currentMessage.content.type === "tips" && <Zap className="w-3 h-3 opacity-60" />}
                            </div>
                          )}
                        </div>
                        <RichContentRenderer
                          content={currentMessage.content}
                          message={currentMessage.message}
                        />
                      </div>
                    </div>
                  )}

                  {/* Progress Indicator */}
                  <div className="flex items-center justify-center gap-1 mt-4">
                    {data.conversation.map((_, index) => (
                      <div
                        key={`progress-${index}`}
                        className="w-2 h-2 rounded-full"
                        style={{
                          backgroundColor: index <= currentStep 
                            ? "var(--emerald)" 
                            : "var(--color-border)"
                        }}
                      />
                    ))}
                  </div>
                </motion.div>
              ) : (
                <motion.div
                  key="completed"
                  initial={{ opacity: 0, scale: 0.95 }}
                  animate={{ opacity: 1, scale: 1 }}
                  className="text-center space-y-4"
                >
                  <div
                    className="p-4 rounded-lg"
                    style={{
                      backgroundColor: "var(--bright-green)15",
                      border: "1px solid var(--bright-green)"
                    }}
                  >
                    <Lightbulb 
                      className="w-8 h-8 mx-auto mb-2" 
                      style={{ color: "var(--bright-green)" }} 
                    />
                    <h3
                      className="text-sm font-semibold mb-2"
                      style={{ color: "var(--charcoal)" }}
                    >
                      Guided Learning Complete!
                    </h3>
                    <p
                      className="text-xs"
                      style={{ color: "var(--grey)" }}
                    >
                      You've completed this guided learning session. Great job!
                    </p>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </div>

          {/* Footer */}
          <div
            className="p-4 border-t"
            style={{ borderColor: "var(--color-border)" }}
          >
            {!isCompleted ? (
              <div className="space-y-2">
                {isUserTurn ? (
                  <div className="flex gap-2">
                    <input
                      type="text"
                      value={userInput}
                      onChange={(e) => setUserInput(e.target.value)}
                      onKeyPress={(e) => e.key === "Enter" && handleUserResponse()}
                      placeholder="Type your response..."
                      className="flex-1 px-3 py-2 rounded-lg text-sm border"
                      style={{
                        backgroundColor: "var(--white)",
                        borderColor: "var(--color-border)",
                        color: "var(--charcoal)"
                      }}
                    />
                    <button
                      onClick={handleUserResponse}
                      disabled={!userInput.trim()}
                      className="px-3 py-2 rounded-lg text-sm font-medium transition-colors duration-200 disabled:opacity-50"
                      style={{
                        backgroundColor: "var(--emerald)",
                        color: "var(--white)"
                      }}
                    >
                      <ArrowRight className="w-4 h-4" />
                    </button>
                  </div>
                ) : (
                  <button
                    onClick={handleNext}
                    className="w-full flex items-center justify-center gap-2 py-2 rounded-lg text-sm font-medium transition-colors duration-200"
                    style={{
                      backgroundColor: "var(--emerald)",
                      color: "var(--white)"
                    }}
                  >
                    Continue
                    <ArrowRight className="w-4 h-4" />
                  </button>
                )}
              </div>
            ) : (
              <button
                onClick={handleReset}
                className="w-full flex items-center justify-center gap-2 py-2 rounded-lg text-sm font-medium transition-colors duration-200"
                style={{
                  backgroundColor: "var(--emerald)",
                  color: "var(--white)"
                }}
              >
                <RotateCcw className="w-3 h-3" />
                Start Over
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

// Example data for preview
export const exampleMentorMeData: MentorMeData[] = [
  {
    id: "example-mentor-1",
    topic: "Understanding API Authentication Methods",
    conversation: [
      {
        id: "1",
        type: "mentor",
        message: "Hi! I'm your AI mentor. Today we'll explore different API authentication methods. Let me show you the most common types:",
        content: {
          type: "table",
          data: {
            headers: ["Method", "Security Level", "Use Case"],
            rows: [
              ["API Key", "Basic", "Simple apps, rate limiting"],
              ["OAuth 2.0", "High", "User authorization, third-party apps"],
              ["JWT", "High", "Stateless authentication"],
              ["Basic Auth", "Low", "Internal services only"]
            ]
          }
        }
      },
      {
        id: "2",
        type: "user",
        message: "Which one should I use for a mobile app?"
      },
      {
        id: "3",
        type: "mentor",
        message: "Great question! For mobile apps, I recommend OAuth 2.0 or JWT. Here's why:",
        content: {
          type: "comparison",
          data: {
            positive: {
              title: "OAuth 2.0 / JWT",
              description: "Secure, scalable, supports token refresh, industry standard"
            },
            negative: {
              title: "API Keys / Basic Auth",
              description: "Less secure, harder to manage, no user context"
            }
          }
        }
      },
      {
        id: "4",
        type: "user",
        message: "How do I implement JWT authentication?"
      },
      {
        id: "5",
        type: "mentor",
        message: "Here's a step-by-step implementation process:",
        content: {
          type: "steps",
          data: {
            steps: [
              {
                title: "User Login",
                description: "User provides credentials (username/password)"
              },
              {
                title: "Server Verification",
                description: "Server validates credentials against database"
              },
              {
                title: "Token Generation",
                description: "Server creates JWT with user info and expiration"
              },
              {
                title: "Client Storage",
                description: "Client stores token securely (not localStorage!)"
              },
              {
                title: "API Requests",
                description: "Include token in Authorization header for each request"
              }
            ]
          }
        }
      },
      {
        id: "6",
        type: "user",
        message: "Can you test my understanding?"
      },
      {
        id: "7",
        type: "mentor",
        message: "Great idea! Let's do a quick knowledge check:",
        content: {
          type: "quiz",
          data: {
            question: "Which HTTP header is used to send JWT tokens?",
            options: [
              "Content-Type",
              "Authorization",
              "X-API-Key",
              "Accept"
            ],
            answer: "B. Authorization - JWT tokens are sent in the Authorization header with 'Bearer' prefix"
          }
        }
      },
      {
        id: "8",
        type: "mentor",
        message: "Let me show you how authentication flows work:",
        content: {
          type: "diagram",
          data: {
            title: "JWT Authentication Flow",
            nodes: [
              {
                label: "User Login",
                description: "User submits credentials"
              },
              {
                label: "Server Validates",
                description: "Check username/password"
              },
              {
                label: "Generate JWT",
                description: "Create signed token"
              },
              {
                label: "Return Token",
                description: "Send JWT to client"
              },
              {
                label: "Use Token",
                description: "Include in API requests"
              }
            ]
          }
        }
      },
      {
        id: "9",
        type: "mentor",
        message: "Here's your current learning progress:",
        content: {
          type: "progress",
          data: {
            skills: [
              { name: "Authentication Basics", level: 90 },
              { name: "JWT Implementation", level: 75 },
              { name: "Security Best Practices", level: 60 },
              { name: "OAuth 2.0", level: 45 }
            ]
          }
        }
      },
      {
        id: "10",
        type: "mentor",
        message: "Here are some pro tips to level up your authentication game:",
        content: {
          type: "tips",
          data: {
            tips: [
              {
                icon: "🔒",
                title: "Use HTTPS Always",
                description: "Never send tokens over unencrypted connections"
              },
              {
                icon: "⏰",
                title: "Short Token Lifespans",
                description: "Keep access tokens short-lived (15-30 minutes)"
              },
              {
                icon: "🔄",
                title: "Implement Refresh",
                description: "Use refresh tokens for seamless re-authentication"
              },
              {
                icon: "🛡️",
                title: "Validate Everything",
                description: "Always verify tokens on the server side"
              }
            ]
          }
        }
      },
      {
        id: "11",
        type: "mentor",
        message: "You've mastered the fundamentals of API authentication! Keep practicing and implementing these concepts.",
        content: {
          type: "highlight",
          data: {
            title: "Key Takeaway",
            description: "Choose authentication methods based on your security needs. OAuth 2.0 and JWT are excellent for modern applications requiring high security and scalability."
          }
        }
      }
    ],
    category: "API Security",
    difficulty: "medium",
    tags: ["APIs", "Security", "Authentication", "JWT", "OAuth"]
  }
];
