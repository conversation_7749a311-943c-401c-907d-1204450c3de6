"use client";

import { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Check, X, Rotate<PERSON>cw, AlertCircle } from "lucide-react";

export interface TrueFalseData {
  id: string;
  statement: string;
  correctAnswer: boolean;
  explanation: string;
  category?: string;
  difficulty?: "easy" | "medium" | "hard";
  tags?: string[];
}

interface TrueFalseProps {
  data: TrueFalseData;
  className?: string;
  onAnswer?: (isCorrect: boolean, selectedAnswer: boolean) => void;
  showResult?: boolean;
}

const difficultyColors = {
  easy: "var(--bright-green)",
  medium: "var(--lime-green)", 
  hard: "var(--emerald-deep)"
};

export default function TrueFalse({ 
  data, 
  className = "", 
  onAnswer,
  showResult = false
}: TrueFalseProps) {
  const [selectedAnswer, setSelectedAnswer] = useState<boolean | null>(null);
  const [showExplanation, setShowExplanation] = useState(false);

  const handleAnswer = (answer: boolean) => {
    setSelectedAnswer(answer);
    setShowExplanation(true);
    const isCorrect = answer === data.correctAnswer;
    onAnswer?.(isCorrect, answer);
  };

  const handleReset = () => {
    setSelectedAnswer(null);
    setShowExplanation(false);
  };

  const isCorrect = selectedAnswer === data.correctAnswer;

  return (
    <div className={`w-96 h-56 ${className}`}>
      <div className="relative w-full h-full">
        <div
          className="w-full h-full rounded-2xl shadow-lg border p-6 flex flex-col"
          style={{
            backgroundColor: "var(--white)",
            borderColor: "var(--color-border)",
            boxShadow: "0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)"
          }}
        >
          {/* Header */}
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-2">
              <div
                className="p-2 rounded-lg"
                style={{ backgroundColor: "var(--bright-green)", color: "var(--white)" }}
              >
                <AlertCircle className="w-4 h-4" />
              </div>
              {data.category && (
                <span
                  className="text-xs font-medium px-2 py-1 rounded-full"
                  style={{
                    backgroundColor: "var(--color-muted)",
                    color: "var(--charcoal)"
                  }}
                >
                  {data.category}
                </span>
              )}
            </div>
            {data.difficulty && (
              <div
                className="w-3 h-3 rounded-full"
                style={{ backgroundColor: difficultyColors[data.difficulty] }}
                title={`Difficulty: ${data.difficulty}`}
              />
            )}
          </div>

          {/* Statement */}
          <div className="flex-1 flex items-center justify-center text-center mb-4">
            <p
              className="text-base font-semibold leading-relaxed"
              style={{ color: "var(--charcoal)" }}
            >
              {data.statement}
            </p>
          </div>

          {/* Answer Buttons or Result */}
          <AnimatePresence mode="wait">
            {!showExplanation ? (
              <motion.div
                key="buttons"
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -10 }}
                className="flex gap-3"
              >
                <button
                  onClick={() => handleAnswer(true)}
                  className="flex-1 flex items-center justify-center gap-2 py-3 rounded-xl text-sm font-semibold transition-all duration-200 hover:scale-[1.02]"
                  style={{
                    backgroundColor: "var(--bright-green)",
                    color: "var(--white)"
                  }}
                >
                  <Check className="w-4 h-4" />
                  True
                </button>
                <button
                  onClick={() => handleAnswer(false)}
                  className="flex-1 flex items-center justify-center gap-2 py-3 rounded-xl text-sm font-semibold transition-all duration-200 hover:scale-[1.02]"
                  style={{
                    backgroundColor: "var(--grey)",
                    color: "var(--white)"
                  }}
                >
                  <X className="w-4 h-4" />
                  False
                </button>
              </motion.div>
            ) : (
              <motion.div
                key="result"
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -10 }}
                className="space-y-3"
              >
                {/* Result Indicator */}
                <div
                  className="flex items-center justify-center gap-2 py-2 rounded-lg"
                  style={{
                    backgroundColor: isCorrect ? "var(--bright-green)" : "var(--grey)",
                    color: "var(--white)"
                  }}
                >
                  {isCorrect ? (
                    <>
                      <Check className="w-4 h-4" />
                      <span className="text-sm font-semibold">Correct!</span>
                    </>
                  ) : (
                    <>
                      <X className="w-4 h-4" />
                      <span className="text-sm font-semibold">Incorrect</span>
                    </>
                  )}
                </div>

                {/* Explanation */}
                <div
                  className="p-3 rounded-lg text-xs leading-relaxed"
                  style={{
                    backgroundColor: "var(--color-muted)",
                    color: "var(--charcoal)"
                  }}
                >
                  <strong>Explanation:</strong> {data.explanation}
                </div>

                {/* Reset Button */}
                <button
                  onClick={handleReset}
                  className="w-full flex items-center justify-center gap-2 py-2 rounded-lg text-xs font-medium transition-colors duration-200"
                  style={{
                    backgroundColor: "var(--emerald)",
                    color: "var(--white)"
                  }}
                >
                  <RotateCcw className="w-3 h-3" />
                  Try Again
                </button>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </div>
    </div>
  );
}

// Example data for preview
export const exampleTrueFalseData: TrueFalseData[] = [
  {
    id: "example-tf-1",
    statement: "JavaScript is a compiled programming language.",
    correctAnswer: false,
    explanation: "JavaScript is an interpreted language, not compiled. It's executed directly by the browser or runtime environment without a separate compilation step.",
    category: "Programming",
    difficulty: "easy",
    tags: ["JavaScript", "Programming Basics"]
  }
];
