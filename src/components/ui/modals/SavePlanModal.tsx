"use client";

import { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { useUserContext } from "@/app/Firebase/Authentication/UserProvider";
import { saveLearningPlan, findExistingPlan, type LearningConcept } from "@/Services/savedLearningPlanService";

interface SavePlanModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSaved: () => void;
  learningPlan: {
    keyword: string;
    concepts: LearningConcept[];
    certificateId: string;
    intensity: "detailed" | "general" | "simple";
    generatedAt: string;
  };
  certificateName: string;
}

export default function SavePlanModal({
  isOpen,
  onClose,
  onSaved,
  learningPlan,
  certificateName,
}: SavePlanModalProps) {
  const { user } = useUserContext();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const [tags, setTags] = useState<string>("");
  const [notes, setNotes] = useState<string>("");

  const handleSave = async () => {
    if (!user) {
      setError("You must be logged in to save learning plans");
      return;
    }

    setLoading(true);
    setError(null);

    try {
      // Check if plan already exists
      const existingPlan = await findExistingPlan(
        user.uid,
        learningPlan.certificateId,
        learningPlan.keyword
      );

      if (existingPlan) {
        setError("A learning plan for this topic already exists. You can edit it from your saved plans.");
        setLoading(false);
        return;
      }

      // Save the plan
      await saveLearningPlan({
        userId: user.uid,
        certificateId: learningPlan.certificateId,
        certificateName,
        keyword: learningPlan.keyword,
        concepts: learningPlan.concepts,
        intensity: learningPlan.intensity,
        generatedAt: learningPlan.generatedAt,
        tags: tags.split(",").map(tag => tag.trim()).filter(tag => tag.length > 0),
        notes: notes.trim(),
      });

      setSuccess(true);
      setTimeout(() => {
        onSaved();
        onClose();
        setSuccess(false);
        setTags("");
        setNotes("");
      }, 1500);

    } catch (err: any) {
      setError(err.message || "Failed to save learning plan");
      setLoading(false);
    }
  };

  const handleClose = () => {
    if (!loading) {
      onClose();
      setError(null);
      setSuccess(false);
      setTags("");
      setNotes("");
    }
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center">
          {/* Backdrop */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="absolute inset-0 bg-black/50 backdrop-blur-sm"
            onClick={handleClose}
          />

          {/* Modal */}
          <motion.div
            initial={{ opacity: 0, scale: 0.95, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.95, y: 20 }}
            className="relative w-full max-w-md mx-4 bg-white rounded-2xl shadow-2xl"
            style={{ backgroundColor: "var(--white)" }}
          >
            {/* Header */}
            <div className="flex items-center justify-between p-6 border-b border-gray-100">
              <div>
                <h2 
                  className="text-xl font-bold"
                  style={{ color: "var(--charcoal)" }}
                >
                  Save Learning Plan
                </h2>
                <p 
                  className="text-sm mt-1"
                  style={{ color: "var(--grey)" }}
                >
                  Save "{learningPlan.keyword}" to your collection
                </p>
              </div>
              <button
                onClick={handleClose}
                disabled={loading}
                className="p-2 rounded-lg hover:bg-gray-100 transition-colors"
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            {/* Content */}
            <div className="p-6 space-y-4">
              {/* Success State */}
              {success && (
                <motion.div
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="flex items-center gap-3 p-4 rounded-lg"
                  style={{ backgroundColor: "var(--bright-green)", color: "var(--white)" }}
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                  <span className="font-medium">Learning plan saved successfully!</span>
                </motion.div>
              )}

              {/* Error State */}
              {error && (
                <div 
                  className="p-4 rounded-lg border"
                  style={{ 
                    backgroundColor: "#fef2f2", 
                    borderColor: "#fecaca",
                    color: "#dc2626" 
                  }}
                >
                  <p className="text-sm font-medium">{error}</p>
                </div>
              )}

              {/* Plan Summary */}
              <div 
                className="p-4 rounded-lg border"
                style={{ backgroundColor: "#f8fffe", borderColor: "var(--bright-green)" }}
              >
                <h3 
                  className="font-semibold mb-2"
                  style={{ color: "var(--charcoal)" }}
                >
                  Plan Summary
                </h3>
                <div className="space-y-1 text-sm" style={{ color: "var(--grey)" }}>
                  <p><span className="font-medium">Certificate:</span> {certificateName}</p>
                  <p><span className="font-medium">Concepts:</span> {learningPlan.concepts.length}</p>
                  <p><span className="font-medium">Level:</span> {learningPlan.intensity}</p>
                </div>
              </div>

              {/* Tags Input */}
              <div>
                <label 
                  className="block text-sm font-medium mb-2"
                  style={{ color: "var(--charcoal)" }}
                >
                  Tags (optional)
                </label>
                <input
                  type="text"
                  value={tags}
                  onChange={(e) => setTags(e.target.value)}
                  placeholder="e.g., important, review, advanced"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                  disabled={loading || success}
                />
                <p className="text-xs mt-1" style={{ color: "var(--grey)" }}>
                  Separate tags with commas
                </p>
              </div>

              {/* Notes Input */}
              <div>
                <label 
                  className="block text-sm font-medium mb-2"
                  style={{ color: "var(--charcoal)" }}
                >
                  Notes (optional)
                </label>
                <textarea
                  value={notes}
                  onChange={(e) => setNotes(e.target.value)}
                  placeholder="Add any personal notes about this learning plan..."
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 resize-none"
                  disabled={loading || success}
                />
              </div>
            </div>

            {/* Footer */}
            <div className="flex items-center justify-end gap-3 p-6 border-t border-gray-100">
              <button
                onClick={handleClose}
                disabled={loading}
                className="px-4 py-2 text-sm font-medium rounded-lg border border-gray-300 hover:bg-gray-50 transition-colors"
                style={{ color: "var(--charcoal)" }}
              >
                Cancel
              </button>
              <button
                onClick={handleSave}
                disabled={loading || success}
                className="inline-flex items-center gap-2 px-4 py-2 text-sm font-medium text-white rounded-lg transition-colors"
                style={{ backgroundColor: loading ? "var(--grey)" : "var(--emerald)" }}
              >
                {loading && (
                  <svg className="w-4 h-4 animate-spin" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" />
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" />
                  </svg>
                )}
                {loading ? "Saving..." : "Save Plan"}
              </button>
            </div>
          </motion.div>
        </div>
      )}
    </AnimatePresence>
  );
}
