"use client";

import { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { useUserContext } from "@/app/Firebase/Authentication/UserProvider";
import { 
  updateLearningPlan, 
  deleteLearningPlan,
  type SavedLearningPlan, 
  type LearningConcept 
} from "@/Services/savedLearningPlanService";

interface EditPlanModalProps {
  isOpen: boolean;
  onClose: () => void;
  onUpdated: () => void;
  plan: SavedLearningPlan;
}

export default function EditPlanModal({
  isOpen,
  onClose,
  onUpdated,
  plan,
}: EditPlanModalProps) {
  const { user } = useUserContext();
  const [loading, setLoading] = useState(false);
  const [deleteLoading, setDeleteLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  
  // Form state
  const [keyword, setKeyword] = useState(plan.keyword);
  const [concepts, setConcepts] = useState<LearningConcept[]>(plan.concepts);
  const [intensity, setIntensity] = useState<"detailed" | "general" | "simple">(plan.intensity);
  const [tags, setTags] = useState<string>(plan.metadata?.tags?.join(", ") || "");
  const [notes, setNotes] = useState<string>(plan.metadata?.notes || "");
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);

  // Reset form when plan changes
  useEffect(() => {
    if (plan) {
      setKeyword(plan.keyword);
      setConcepts(plan.concepts);
      setIntensity(plan.intensity);
      setTags(plan.metadata?.tags?.join(", ") || "");
      setNotes(plan.metadata?.notes || "");
    }
  }, [plan]);

  const handleUpdate = async () => {
    if (!user || !plan.id) {
      setError("Unable to update plan");
      return;
    }

    setLoading(true);
    setError(null);

    try {
      await updateLearningPlan(user.uid, plan.id, {
        keyword: keyword.trim(),
        concepts,
        intensity,
        tags: tags.split(",").map(tag => tag.trim()).filter(tag => tag.length > 0),
        notes: notes.trim(),
      });

      setSuccess(true);
      setTimeout(() => {
        onUpdated();
        onClose();
        setSuccess(false);
      }, 1500);

    } catch (err: any) {
      setError(err.message || "Failed to update learning plan");
      setLoading(false);
    }
  };

  const handleDelete = async () => {
    if (!user || !plan.id) {
      setError("Unable to delete plan");
      return;
    }

    setDeleteLoading(true);
    setError(null);

    try {
      await deleteLearningPlan(user.uid, plan.id);
      onUpdated();
      onClose();
    } catch (err: any) {
      setError(err.message || "Failed to delete learning plan");
      setDeleteLoading(false);
    }
  };

  const addConcept = () => {
    setConcepts([...concepts, {
      title: "",
      description: "",
      prerequisites: [],
      focusArea: "",
    }]);
  };

  const updateConcept = (index: number, field: keyof LearningConcept, value: string | string[]) => {
    const updated = [...concepts];
    updated[index] = { ...updated[index], [field]: value };
    setConcepts(updated);
  };

  const removeConcept = (index: number) => {
    setConcepts(concepts.filter((_, i) => i !== index));
  };

  const handleClose = () => {
    if (!loading && !deleteLoading) {
      onClose();
      setError(null);
      setSuccess(false);
      setShowDeleteConfirm(false);
    }
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center">
          {/* Backdrop */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="absolute inset-0 bg-black/50 backdrop-blur-sm"
            onClick={handleClose}
          />

          {/* Modal */}
          <motion.div
            initial={{ opacity: 0, scale: 0.95, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.95, y: 20 }}
            className="relative w-full max-w-2xl mx-4 bg-white rounded-2xl shadow-2xl max-h-[90vh] overflow-hidden"
            style={{ backgroundColor: "var(--white)" }}
          >
            {/* Header */}
            <div className="flex items-center justify-between p-6 border-b border-gray-100">
              <div>
                <h2 
                  className="text-xl font-bold"
                  style={{ color: "var(--charcoal)" }}
                >
                  Edit Learning Plan
                </h2>
                <p 
                  className="text-sm mt-1"
                  style={{ color: "var(--grey)" }}
                >
                  Modify your "{plan.keyword}" learning plan
                </p>
              </div>
              <button
                onClick={handleClose}
                disabled={loading || deleteLoading}
                className="p-2 rounded-lg hover:bg-gray-100 transition-colors"
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            {/* Content */}
            <div className="p-6 space-y-6 overflow-y-auto max-h-[calc(90vh-200px)]">
              {/* Success State */}
              {success && (
                <motion.div
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="flex items-center gap-3 p-4 rounded-lg"
                  style={{ backgroundColor: "var(--bright-green)", color: "var(--white)" }}
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                  <span className="font-medium">Learning plan updated successfully!</span>
                </motion.div>
              )}

              {/* Error State */}
              {error && (
                <div 
                  className="p-4 rounded-lg border"
                  style={{ 
                    backgroundColor: "#fef2f2", 
                    borderColor: "#fecaca",
                    color: "#dc2626" 
                  }}
                >
                  <p className="text-sm font-medium">{error}</p>
                </div>
              )}

              {/* Basic Info */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label 
                    className="block text-sm font-medium mb-2"
                    style={{ color: "var(--charcoal)" }}
                  >
                    Topic/Keyword
                  </label>
                  <input
                    type="text"
                    value={keyword}
                    onChange={(e) => setKeyword(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                    disabled={loading || deleteLoading || success}
                  />
                </div>

                <div>
                  <label 
                    className="block text-sm font-medium mb-2"
                    style={{ color: "var(--charcoal)" }}
                  >
                    Intensity Level
                  </label>
                  <select
                    value={intensity}
                    onChange={(e) => setIntensity(e.target.value as "detailed" | "general" | "simple")}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                    disabled={loading || deleteLoading || success}
                  >
                    <option value="simple">Simple</option>
                    <option value="general">General</option>
                    <option value="detailed">Detailed</option>
                  </select>
                </div>
              </div>

              {/* Tags and Notes */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label 
                    className="block text-sm font-medium mb-2"
                    style={{ color: "var(--charcoal)" }}
                  >
                    Tags
                  </label>
                  <input
                    type="text"
                    value={tags}
                    onChange={(e) => setTags(e.target.value)}
                    placeholder="e.g., important, review, advanced"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                    disabled={loading || deleteLoading || success}
                  />
                </div>

                <div>
                  <label 
                    className="block text-sm font-medium mb-2"
                    style={{ color: "var(--charcoal)" }}
                  >
                    Notes
                  </label>
                  <textarea
                    value={notes}
                    onChange={(e) => setNotes(e.target.value)}
                    placeholder="Personal notes..."
                    rows={2}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 resize-none"
                    disabled={loading || deleteLoading || success}
                  />
                </div>
              </div>

              {/* Concepts */}
              <div>
                <div className="flex items-center justify-between mb-4">
                  <h3 
                    className="text-lg font-semibold"
                    style={{ color: "var(--charcoal)" }}
                  >
                    Learning Concepts ({concepts.length})
                  </h3>
                  <button
                    onClick={addConcept}
                    disabled={loading || deleteLoading || success}
                    className="inline-flex items-center gap-2 px-3 py-1 text-sm font-medium text-white rounded-lg transition-colors"
                    style={{ backgroundColor: "var(--emerald)" }}
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                    </svg>
                    Add Concept
                  </button>
                </div>

                <div className="space-y-4">
                  {concepts.map((concept, index) => (
                    <div 
                      key={index}
                      className="p-4 border border-gray-200 rounded-lg space-y-3"
                    >
                      <div className="flex items-center justify-between">
                        <span 
                          className="text-sm font-medium"
                          style={{ color: "var(--grey)" }}
                        >
                          Concept {index + 1}
                        </span>
                        {concepts.length > 1 && (
                          <button
                            onClick={() => removeConcept(index)}
                            disabled={loading || deleteLoading || success}
                            className="p-1 text-red-500 hover:bg-red-50 rounded"
                          >
                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                            </svg>
                          </button>
                        )}
                      </div>

                      <input
                        type="text"
                        value={concept.title}
                        onChange={(e) => updateConcept(index, "title", e.target.value)}
                        placeholder="Concept title"
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                        disabled={loading || deleteLoading || success}
                      />

                      <textarea
                        value={concept.description}
                        onChange={(e) => updateConcept(index, "description", e.target.value)}
                        placeholder="Concept description"
                        rows={2}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 resize-none"
                        disabled={loading || deleteLoading || success}
                      />

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                        <input
                          type="text"
                          value={concept.prerequisites.join(", ")}
                          onChange={(e) => updateConcept(index, "prerequisites", e.target.value.split(",").map(p => p.trim()).filter(p => p))}
                          placeholder="Prerequisites (comma-separated)"
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                          disabled={loading || deleteLoading || success}
                        />

                        <input
                          type="text"
                          value={concept.focusArea}
                          onChange={(e) => updateConcept(index, "focusArea", e.target.value)}
                          placeholder="Focus area"
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                          disabled={loading || deleteLoading || success}
                        />
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Footer */}
            <div className="flex items-center justify-between p-6 border-t border-gray-100">
              <div>
                {!showDeleteConfirm ? (
                  <button
                    onClick={() => setShowDeleteConfirm(true)}
                    disabled={loading || deleteLoading || success}
                    className="px-4 py-2 text-sm font-medium text-red-600 border border-red-300 rounded-lg hover:bg-red-50 transition-colors"
                  >
                    Delete Plan
                  </button>
                ) : (
                  <div className="flex items-center gap-2">
                    <span className="text-sm text-red-600">Are you sure?</span>
                    <button
                      onClick={handleDelete}
                      disabled={deleteLoading}
                      className="px-3 py-1 text-sm font-medium text-white bg-red-600 rounded hover:bg-red-700 transition-colors"
                    >
                      {deleteLoading ? "Deleting..." : "Yes, Delete"}
                    </button>
                    <button
                      onClick={() => setShowDeleteConfirm(false)}
                      disabled={deleteLoading}
                      className="px-3 py-1 text-sm font-medium text-gray-600 border border-gray-300 rounded hover:bg-gray-50 transition-colors"
                    >
                      Cancel
                    </button>
                  </div>
                )}
              </div>

              <div className="flex items-center gap-3">
                <button
                  onClick={handleClose}
                  disabled={loading || deleteLoading}
                  className="px-4 py-2 text-sm font-medium rounded-lg border border-gray-300 hover:bg-gray-50 transition-colors"
                  style={{ color: "var(--charcoal)" }}
                >
                  Cancel
                </button>
                <button
                  onClick={handleUpdate}
                  disabled={loading || deleteLoading || success}
                  className="inline-flex items-center gap-2 px-4 py-2 text-sm font-medium text-white rounded-lg transition-colors"
                  style={{ backgroundColor: loading ? "var(--grey)" : "var(--emerald)" }}
                >
                  {loading && (
                    <svg className="w-4 h-4 animate-spin" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" />
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" />
                    </svg>
                  )}
                  {loading ? "Updating..." : "Update Plan"}
                </button>
              </div>
            </div>
          </motion.div>
        </div>
      )}
    </AnimatePresence>
  );
}
