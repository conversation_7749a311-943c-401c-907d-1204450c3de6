"use client";

import { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { useRouter } from "next/navigation";
import { useLocale } from "next-intl";

interface LearningConcept {
  title: string;
  description: string;
  prerequisites: string[];
  focusArea: string;
}

interface LearningPlanModalProps {
  isOpen: boolean;
  onClose: () => void;
  keyword: string;
  certificateId: string;
  intensity: "detailed" | "general" | "simple";
}

export default function LearningPlanModal({
  isOpen,
  onClose,
  keyword,
  certificateId,
  intensity,
}: LearningPlanModalProps) {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();
  const locale = useLocale();

  useEffect(() => {
    if (isOpen && keyword && certificateId) {
      generateLearningPlan();
    }
  }, [isOpen, keyword, certificateId, intensity]);

  const generateLearningPlan = async () => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch("/api/ai/learning/generate-plan", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          keyword,
          certificateId,
          intensity,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Failed to generate learning plan");
      }

      if (!data.valid) {
        setError(data.feedback);
        setLoading(false);
        return;
      }

      // Store the learning plan and navigate
      const conceptName = encodeURIComponent(keyword.trim());
      const learningPlan = {
        keyword,
        concepts: data.concepts,
        certificateId,
        intensity,
        generatedAt: new Date().toISOString(),
      };

      // Store in sessionStorage for the timeline page
      const storageKey = `learning-plan-${conceptName}`;
      sessionStorage.setItem(storageKey, JSON.stringify(learningPlan));

      // Navigate to timeline page
      router.push(`/${locale}/dashboard/knowledge-hub/certificates/${certificateId}/C${conceptName}`);
      
      // Close modal after navigation
      onClose();
    } catch (err: any) {
      setError(err.message || "An error occurred while generating the learning plan");
      setLoading(false);
    }
  };

  const DotLoader = () => (
    <div className="flex space-x-2 justify-center items-center">
      {[0, 1, 2].map((i) => (
        <motion.div
          key={i}
          className="w-3 h-3 bg-[var(--emerald)] rounded-full"
          animate={{
            scale: [1, 1.2, 1],
            opacity: [0.7, 1, 0.7],
          }}
          transition={{
            duration: 1.5,
            repeat: Infinity,
            delay: i * 0.2,
          }}
        />
      ))}
    </div>
  );

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
          onClick={(e) => {
            if (e.target === e.currentTarget && !loading) {
              onClose();
            }
          }}
        >
          <motion.div
            initial={{ scale: 0.95, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.95, opacity: 0 }}
            className="bg-white rounded-2xl p-8 max-w-md w-full mx-auto shadow-2xl"
            style={{
              background: "var(--white)",
              border: "1px solid var(--color-border)",
            }}
          >
            {loading && (
              <div className="text-center">
                <div className="mb-6">
                  <DotLoader />
                </div>
                <h3 className="text-xl font-semibold mb-2" style={{ color: "var(--charcoal)" }}>
                  Generating Your Learning Plan
                </h3>
                <p className="text-sm" style={{ color: "var(--grey)" }}>
                  Creating a personalized learning journey for "{keyword}"...
                </p>
              </div>
            )}

            {error && (
              <div className="text-center">
                <div className="mb-4 p-4 rounded-lg bg-red-50 border border-red-200">
                  <h3 className="text-lg font-semibold text-red-800 mb-2">
                    Topic Not Supported
                  </h3>
                  <p className="text-sm text-red-700">{error}</p>
                </div>
                <button
                  onClick={onClose}
                  className="px-6 py-2 rounded-full text-white font-medium transition"
                  style={{
                    backgroundColor: "var(--emerald)",
                  }}
                >
                  Try Another Topic
                </button>
              </div>
            )}
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
}
