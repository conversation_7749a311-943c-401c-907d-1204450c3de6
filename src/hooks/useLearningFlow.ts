"use client";

import { useState, useEffect, useCallback } from "react";
import { 
  LearningService, 
  type LearningSession, 
  type ConceptExplanation, 
  type PracticalScenario,
  type InteractiveElement 
} from "@/Services/learningService";

interface UseLearningFlowReturn {
  // State
  session: LearningSession | null;
  loading: boolean;
  error: string | null;
  currentPhase: "explanation" | "scenario" | "completed";
  
  // Actions
  startLearning: (conceptName: string, intensity?: "simple" | "general" | "detailed") => Promise<void>;
  proceedToScenario: (difficulty?: "beginner" | "intermediate" | "advanced") => Promise<void>;
  completeSession: () => void;
  resetSession: () => void;
  
  // Progress
  progress: {
    phase: string;
    timeSpent: number;
    completionPercentage: number;
  };
}

export function useLearningFlow(): UseLearningFlowReturn {
  const [sessionId, setSessionId] = useState<string | null>(null);
  const [session, setSession] = useState<LearningSession | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [progress, setProgress] = useState({
    phase: "unknown",
    timeSpent: 0,
    completionPercentage: 0,
  });

  const learningService = LearningService.getInstance();

  // Update progress periodically
  useEffect(() => {
    if (!sessionId) return;

    const updateProgress = () => {
      const currentProgress = learningService.getSessionProgress(sessionId);
      setProgress(currentProgress);
    };

    updateProgress();
    const interval = setInterval(updateProgress, 1000);

    return () => clearInterval(interval);
  }, [sessionId, learningService]);

  const startLearning = useCallback(async (
    conceptName: string, 
    intensity: "simple" | "general" | "detailed" = "general"
  ) => {
    try {
      setLoading(true);
      setError(null);

      // Create new learning session
      const newSessionId = learningService.createLearningSession(conceptName);
      setSessionId(newSessionId);

      // Generate concept explanation
      const { explanation, practicalScenarioPrompt } = await learningService.generateConceptExplanation(
        conceptName,
        intensity
      );

      // Update session with explanation
      learningService.updateLearningSession(newSessionId, {
        explanation,
        practicalScenarioPrompt,
        currentPhase: "explanation",
      });

      // Get updated session
      const updatedSession = learningService.getLearningSession(newSessionId);
      setSession(updatedSession);

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Failed to start learning session";
      setError(errorMessage);
      console.error("Error starting learning:", err);
    } finally {
      setLoading(false);
    }
  }, [learningService]);

  const proceedToScenario = useCallback(async (
    difficulty: "beginner" | "intermediate" | "advanced" = "intermediate"
  ) => {
    if (!sessionId || !session) {
      setError("No active learning session");
      return;
    }

    try {
      setLoading(true);
      setError(null);

      // Generate practical scenario
      const { scenario, interactiveElements } = await learningService.generatePracticalScenario(
        session.conceptName,
        session.practicalScenarioPrompt,
        difficulty
      );

      // Update session with scenario
      learningService.updateLearningSession(sessionId, {
        scenario,
        interactiveElements,
        currentPhase: "scenario",
      });

      // Get updated session
      const updatedSession = learningService.getLearningSession(sessionId);
      setSession(updatedSession);

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Failed to generate practical scenario";
      setError(errorMessage);
      console.error("Error generating scenario:", err);
    } finally {
      setLoading(false);
    }
  }, [sessionId, session, learningService]);

  const completeSession = useCallback(() => {
    if (!sessionId) return;

    learningService.updateLearningSession(sessionId, {
      currentPhase: "completed",
    });

    const updatedSession = learningService.getLearningSession(sessionId);
    setSession(updatedSession);
  }, [sessionId, learningService]);

  const resetSession = useCallback(() => {
    if (sessionId) {
      learningService.deleteLearningSession(sessionId);
    }
    setSessionId(null);
    setSession(null);
    setError(null);
    setProgress({
      phase: "unknown",
      timeSpent: 0,
      completionPercentage: 0,
    });
  }, [sessionId, learningService]);

  return {
    session,
    loading,
    error,
    currentPhase: session?.currentPhase || "explanation",
    startLearning,
    proceedToScenario,
    completeSession,
    resetSession,
    progress,
  };
}
