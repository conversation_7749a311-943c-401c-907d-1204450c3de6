"use client";

import { useEffect, useState } from "react";
import { useParams, useRouter } from "next/navigation";
import { useLocale } from "next-intl";
import { LearningTimeline } from "@/components/ui/timeline/LearningTimeline";
import { getCertificate } from "@/Services/certificateDetails";
import { DtcHero } from "@/components/ui/Hero";
import { useUserContext } from "@/app/Firebase/Authentication/UserProvider";
import SavePlanModal from "@/components/ui/modals/SavePlanModal";
import EditPlanModal from "@/components/ui/modals/EditPlanModal";
import {
  findExistingPlan,
  getLearningPlan,
  type SavedLearningPlan
} from "@/Services/savedLearningPlanService";

interface LearningConcept {
  title: string;
  description: string;
  prerequisites: string[];
  focusArea: string;
}

interface LearningPlan {
  keyword: string;
  concepts: LearningConcept[];
  certificateId: string;
  intensity: string;
  generatedAt: string;
}

export default function LearningConceptPage() {
  const params = useParams();
  const router = useRouter();
  const locale = useLocale();
  const { user } = useUserContext();

  const framework = Array.isArray(params?.framework)
    ? params?.framework[0]
    : (params?.framework as string | undefined);

  const concept = Array.isArray(params?.concept)
    ? params?.concept[0]
    : (params?.concept as string | undefined);

  const [learningPlan, setLearningPlan] = useState<LearningPlan | null>(null);
  const [certificateName, setCertificateName] = useState<string>("");
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Modal states
  const [showSaveModal, setShowSaveModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [existingSavedPlan, setExistingSavedPlan] = useState<SavedLearningPlan | null>(null);
  const [checkingExistingPlan, setCheckingExistingPlan] = useState(false);

  useEffect(() => {
    if (!concept || !framework) {
      setError("Invalid concept or framework");
      setLoading(false);
      return;
    }

    loadLearningPlan();
  }, [concept, framework]);

  const loadLearningPlan = async () => {
    try {
      // Get the learning plan from sessionStorage
      // The concept parameter includes the 'C' prefix, so we need to remove it
      // It comes URL encoded, so we need to decode it first
      const decodedConcept = concept ? decodeURIComponent(concept) : '';

      // Remove the 'C' prefix to get the original keyword
      const originalKeyword = decodedConcept.startsWith('C') ? decodedConcept.slice(1) : decodedConcept;

      // The sessionStorage key was stored with the encoded version of the original keyword
      const encodedKey = encodeURIComponent(originalKeyword);
      const planKey = `learning-plan-${encodedKey}`;

      const storedPlan = sessionStorage.getItem(planKey);

      if (!storedPlan) {
        setError("Learning plan not found. Please generate a new plan.");
        setLoading(false);
        return;
      }

      const plan: LearningPlan = JSON.parse(storedPlan);
      setLearningPlan(plan);

      // Get certificate name
      if (framework) {
        const certificate = await getCertificate(framework);
        if (certificate) {
          setCertificateName(certificate.name);
        }
      }

      setLoading(false);
    } catch (err) {
      console.error("Error loading learning plan:", err);
      setError("Failed to load learning plan");
      setLoading(false);
    }
  };

  const handleBackToHub = () => {
    router.push(`/${locale}/dashboard/knowledge-hub/certificates/${framework}`);
  };

  // Check if plan already exists when user clicks save
  const handleSavePlan = async () => {
    if (!user || !learningPlan || !framework) return;

    setCheckingExistingPlan(true);
    try {
      const existing = await findExistingPlan(user.uid, framework, learningPlan.keyword);
      if (existing) {
        setExistingSavedPlan(existing);
        setShowEditModal(true);
      } else {
        setShowSaveModal(true);
      }
    } catch (error) {
      console.error("Error checking existing plan:", error);
      setShowSaveModal(true); // Fallback to save modal
    } finally {
      setCheckingExistingPlan(false);
    }
  };

  // Handle edit plan
  const handleEditPlan = async () => {
    if (!user || !learningPlan || !framework) return;

    setCheckingExistingPlan(true);
    try {
      const existing = await findExistingPlan(user.uid, framework, learningPlan.keyword);
      if (existing) {
        setExistingSavedPlan(existing);
        setShowEditModal(true);
      } else {
        // If no existing plan, show save modal instead
        setShowSaveModal(true);
      }
    } catch (error) {
      console.error("Error finding plan to edit:", error);
      setShowSaveModal(true); // Fallback to save modal
    } finally {
      setCheckingExistingPlan(false);
    }
  };

  // Handle modal close and refresh
  const handleModalClose = () => {
    setShowSaveModal(false);
    setShowEditModal(false);
    setExistingSavedPlan(null);
  };

  // Handle successful save/update
  const handlePlanSaved = () => {
    // Could show a success message or refresh data here
    console.log("Plan saved successfully!");
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="flex space-x-2 justify-center items-center mb-4">
            {[0, 1, 2].map((i) => (
              <div
                key={i}
                className="w-3 h-3 rounded-full animate-pulse"
                style={{ 
                  backgroundColor: "var(--emerald)",
                  animationDelay: `${i * 0.2}s` 
                }}
              />
            ))}
          </div>
          <p style={{ color: "var(--grey)" }}>Loading your learning plan...</p>
        </div>
      </div>
    );
  }

  if (error || !learningPlan) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center max-w-md mx-auto p-6">
          <div className="mb-6">
            <svg 
              className="w-16 h-16 mx-auto mb-4" 
              fill="none" 
              stroke="currentColor" 
              viewBox="0 0 24 24"
              style={{ color: "var(--grey)" }}
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
          <h2 className="text-xl font-semibold mb-2" style={{ color: "var(--charcoal)" }}>
            Learning Plan Not Found
          </h2>
          <p className="mb-6" style={{ color: "var(--grey)" }}>
            {error || "The learning plan for this concept could not be found."}
          </p>
          <button
            onClick={handleBackToHub}
            className="px-6 py-3 rounded-full font-medium transition"
            style={{
              backgroundColor: "var(--emerald)",
              color: "var(--white)",
            }}
          >
            Back to Learning Hub
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* DTC Hero Section */}
      <DtcHero
        title={learningPlan.keyword}
        subtitle={`${certificateName} • AI-Generated Learning Journey`}
        image="hero2"
      >
        <div className="w-full max-w-6xl mx-auto">
          {/* Top Row: Back Button */}
          <div className="flex justify-start mb-8">
            <button
              onClick={handleBackToHub}
              className="inline-flex items-center gap-2 px-4 py-2 bg-white/10 backdrop-blur-sm rounded-lg border border-white/20 text-white/90 hover:text-white hover:bg-white/20 transition-all duration-200"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
              Back to Learning Hub
            </button>
          </div>

          {/* Stats Row */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            {/* Concepts Count */}
            <div className="bg-white/15 backdrop-blur-sm rounded-xl p-5 border border-white/20 text-center">
              <div className="text-3xl font-bold text-white mb-1">
                {learningPlan.concepts.length}
              </div>
              <div className="text-sm text-white/80 font-medium">
                Learning Concepts
              </div>
            </div>

            {/* Difficulty Level */}
            <div className="bg-white/15 backdrop-blur-sm rounded-xl p-5 border border-white/20 text-center">
              <div className="text-xl font-semibold capitalize text-white mb-1">
                {learningPlan.intensity}
              </div>
              <div className="text-sm text-white/80 font-medium">
                Difficulty Level
              </div>
            </div>

            {/* Estimated Time */}
            <div className="bg-white/15 backdrop-blur-sm rounded-xl p-5 border border-white/20 text-center">
              <div className="text-xl font-bold text-white mb-1">
                ~{learningPlan.concepts.length * 30}m
              </div>
              <div className="text-sm text-white/80 font-medium">
                Total Time
              </div>
            </div>

            {/* Generated Date */}
            <div className="bg-white/15 backdrop-blur-sm rounded-xl p-5 border border-white/20 text-center">
              <div className="text-sm font-medium text-white mb-1">
                {new Date(learningPlan.generatedAt).toLocaleDateString()}
              </div>
              <div className="text-xs text-white/80 font-medium">
                Generated
              </div>
            </div>
          </div>

          {/* Action Buttons Row */}
          <div className="flex flex-wrap items-center justify-center gap-4">
            <button
              onClick={handleSavePlan}
              disabled={checkingExistingPlan || !user}
              className="inline-flex items-center gap-2 px-6 py-3 bg-white text-emerald font-semibold rounded-xl hover:bg-white/90 transition-all duration-200 shadow-lg disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {checkingExistingPlan ? (
                <svg className="w-5 h-5 animate-spin" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" />
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" />
                </svg>
              ) : (
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z" />
                </svg>
              )}
              {checkingExistingPlan ? "Checking..." : "Save Plan"}
            </button>

            <button
              onClick={handleEditPlan}
              disabled={checkingExistingPlan || !user}
              className="inline-flex items-center gap-2 px-6 py-3 bg-white/10 backdrop-blur-sm text-white border border-white/20 rounded-xl hover:bg-white/20 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {checkingExistingPlan ? (
                <svg className="w-5 h-5 animate-spin" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" />
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" />
                </svg>
              ) : (
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                </svg>
              )}
              {checkingExistingPlan ? "Checking..." : "Edit Plan"}
            </button>

            <button className="inline-flex items-center gap-2 px-6 py-3 bg-white/10 backdrop-blur-sm text-white border border-white/20 rounded-xl hover:bg-white/20 transition-all duration-200">
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z" />
              </svg>
              Share
            </button>
          </div>
        </div>
      </DtcHero>

      {/* Timeline */}
      <LearningTimeline
        keyword={learningPlan.keyword}
        concepts={learningPlan.concepts}
      />

      {/* Save Plan Modal */}
      {learningPlan && (
        <SavePlanModal
          isOpen={showSaveModal}
          onClose={handleModalClose}
          onSaved={handlePlanSaved}
          learningPlan={{
            keyword: learningPlan.keyword,
            concepts: learningPlan.concepts,
            certificateId: learningPlan.certificateId,
            intensity: learningPlan.intensity as "detailed" | "general" | "simple",
            generatedAt: learningPlan.generatedAt,
          }}
          certificateName={certificateName}
        />
      )}

      {/* Edit Plan Modal */}
      {existingSavedPlan && (
        <EditPlanModal
          isOpen={showEditModal}
          onClose={handleModalClose}
          onUpdated={handlePlanSaved}
          plan={existingSavedPlan}
        />
      )}
    </div>
  );
}
