import { NextRequest } from "next/server";
import { google } from "@ai-sdk/google";
import { generateObject } from "ai";
import { z } from "zod";
import { getFirebaseAdmin } from "@/app/Firebase/admin";

const GeneratePlanSchema = z.object({
  keyword: z.string().min(1),
  certificateId: z.string().min(1),
  intensity: z.enum(["detailed", "general", "simple"]).optional().default("general"),
});

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const parsed = GeneratePlanSchema.safeParse(body);
    
    if (!parsed.success) {
      return Response.json({ error: "Invalid request data", details: parsed.error.issues }, { status: 400 });
    }

    const { keyword, certificateId, intensity } = parsed.data;

    // Get certificate context using Firebase Admin
    const { db } = await getFirebaseAdmin();
    const certificateDoc = await db.collection('certificates').doc(certificateId).get();

    if (!certificateDoc.exists) {
      return Response.json({ error: "Certificate not found" }, { status: 404 });
    }

    const certificate = { id: certificateDoc.id, ...certificateDoc.data() };

    const systemPrompt = buildSystemPrompt({ certificate, intensity });
    const userPrompt = buildUserPrompt(keyword);

    const result = await generateObject({
      model: google("gemini-2.5-pro"),
      schema: z.object({
        isValid: z.boolean(),
        feedback: z.string().optional(),
        concepts: z.array(
          z.object({
            title: z.string(),
            description: z.string(),
            prerequisites: z.array(z.string()),
            focusArea: z.string(),
          })
        ).optional(),
      }),
      prompt: `${systemPrompt}\n\n${userPrompt}`,
    });

    const response = result.object;

    if (!response.isValid) {
      return Response.json({ 
        valid: false, 
        feedback: response.feedback || "This topic is not related to the certificate content. Please enter a topic relevant to the certification." 
      });
    }

    return Response.json({ 
      valid: true, 
      concepts: response.concepts || [],
      usage: result.usage 
    });

  } catch (e: any) {
    console.error("Learning plan generation error:", e);
    return Response.json({ error: e?.message ?? "Server error" }, { status: 500 });
  }
}

function buildSystemPrompt({ certificate, intensity }: { certificate: any; intensity: string }) {
  const intensityHint = intensity === "detailed" 
    ? "Provide comprehensive, in-depth concepts with detailed explanations."
    : intensity === "simple"
    ? "Keep concepts simple and beginner-friendly with basic explanations."
    : "Provide balanced concepts suitable for intermediate learners.";

  return [
    `You are an expert learning plan generator for enterprise data management and cybersecurity certifications.`,
    `Your task is to validate if a keyword is relevant to the certificate and generate an instant tutoring plan.`,
    ``,
    `Certificate Context:`,
    `- Name: ${certificate.name}`,
    `- Provider: ${certificate.provider}`,
    `- Description: ${certificate.description}`,
    `- Domain: ${certificate.domain}`,
    ``,
    `IMPORTANT RULES:`,
    `1. ONLY accept keywords that are directly related to this certificate's content and domain`,
    `2. If the keyword is NOT related to the certificate, set isValid: false and provide helpful feedback`,
    `3. If valid, generate 3-7 focused learning concepts for INSTANT tutoring (not long-term planning)`,
    `4. Each concept should be learnable in one focused session`,
    `5. Focus on practical, actionable learning`,
    ``,
    `Intensity Level: ${intensityHint}`,
    ``,
    `For valid keywords, provide concepts with:`,
    `- Clear, specific titles`,
    `- Practical descriptions of what to learn`,
    `- Prerequisites (if any)`,
    `- Focus area within the certificate domain`,
  ].join("\n");
}

function buildUserPrompt(keyword: string) {
  return [
    `Keyword to analyze: "${keyword}"`,
    ``,
    `First, determine if this keyword is relevant to the certificate content.`,
    `If not relevant, explain why and suggest related topics.`,
    `If relevant, generate an instant learning plan with focused concepts.`,
  ].join("\n");
}
